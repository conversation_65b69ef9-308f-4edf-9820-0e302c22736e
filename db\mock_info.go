package db

import (
	"math/rand"
	"time"

	"train_expert_system/models"

	logger "github.com/sirupsen/logrus"
)

// 模拟数据库查询的延迟范围（毫秒）
const (
	MinQueryDelay = 10  // 最小延迟 10ms
	MaxQueryDelay = 100 // 最大延迟 100ms
)

// 模拟的总记录数
const TotalRecords = 10000

// UseMockData 控制是否使用模拟数据
var UseMockData = false

// generateRandomInfo 生成一个包含随机数据的Info对象
func generateRandomInfo() models.Info {
	// 生成随机ID
	randomID := uint(rand.Intn(100000) + 1)

	// 生成随机名称
	nameLength := rand.Intn(6) + 5 // 5-10个字符
	randomName := generateRandomString(nameLength)

	// 生成随机值
	randomValue := rand.Intn(1000) + 1

	// 生成随机消息
	messageLength := rand.Intn(31) + 20 // 20-50个字符
	randomMessage := generateRandomString(messageLength)

	return models.Info{
		ID:      randomID,
		Name:    randomName,
		Value:   randomValue,
		Message: randomMessage,
	}
}

// generateRandomString 生成指定长度的随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 "
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// MockQueryInfo 模拟查询info表数据，返回随机生成的数据
func MockQueryInfo(limit, offset int, countTotal bool) ([]models.Info, int64, error) {
	startTime := time.Now()
	logger.Infof("开始模拟查询info表，限制 %d 行，偏移量 %d", limit, offset)

	// 模拟数据库查询延迟
	queryDelay := time.Duration(rand.Intn(MaxQueryDelay-MinQueryDelay)+MinQueryDelay) * time.Millisecond
	time.Sleep(queryDelay)

	// 计算实际返回的记录数（不能超过总记录数）
	actualLimit := limit
	if offset+limit > TotalRecords {
		actualLimit = TotalRecords - offset
		if actualLimit < 0 {
			actualLimit = 0
		}
	}

	// 生成随机数据
	results := make([]models.Info, actualLimit)
	for i := 0; i < actualLimit; i++ {
		results[i] = generateRandomInfo()
	}

	// 如果需要，返回总记录数
	var total int64
	if countTotal {
		total = TotalRecords
	}

	duration := time.Since(startTime)
	logger.Infof("模拟查询成功，返回 %d 条记录，耗时 %.3f 秒", len(results), duration.Seconds())

	return results, total, nil
}

// MockInsertInfo 模拟插入单条记录
func MockInsertInfo(info *models.Info) error {
	startTime := time.Now()
	logger.Infof("开始模拟插入单条记录，name=%s", info.Name)

	// 模拟插入延迟
	insertDelay := time.Duration(rand.Intn(50)+10) * time.Millisecond
	time.Sleep(insertDelay)

	// 模拟生成ID
	info.ID = uint(rand.Intn(100000) + 1)

	duration := time.Since(startTime)
	logger.Infof("模拟插入成功，ID=%d，耗时 %.3f 秒", info.ID, duration.Seconds())

	return nil
}

// MockBatchInsertInfo 模拟批量插入记录
func MockBatchInsertInfo(infoList []models.Info) error {
	startTime := time.Now()
	count := len(infoList)
	logger.Infof("开始模拟批量插入 %d 条记录", count)

	// 模拟批量插入延迟
	batchDelay := time.Duration(count*2) * time.Millisecond
	time.Sleep(batchDelay)

	// 模拟为每条记录生成ID
	for i := range infoList {
		infoList[i].ID = uint(rand.Intn(100000) + 1)
	}

	duration := time.Since(startTime)
	perRecord := duration.Seconds() / float64(count)
	logger.Infof("模拟批量插入成功，共 %d 条记录，总耗时 %.3f 秒，平均每条 %.5f 秒",
		count, duration.Seconds(), perRecord)

	return nil
}
