package main

import (
	"context"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"train_expert_system/routes"

	logger "github.com/sirupsen/logrus"

	"github.com/gin-gonic/gin"
)

func init() {
	// 初始化日志
	logger.SetFormatter(&logger.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
	})
	logFile, err := os.OpenFile("./log/system.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err == nil {
		logger.SetOutput(io.MultiWriter(os.Stdout, logFile))
	} else {
		logger.Warnf("Failed to log to file, using default stderr")
		logger.SetOutput(os.Stdout)
	}
	logger.SetLevel(logger.InfoLevel)
}

func main() {
	// 初始化日志
	logger.Infof("启动列车信息查询API服务...")

	// 初始化数据库连接（可选，当前使用模拟服务）
	// 如果要使用真实数据库，请取消注释以下代码
	/*
		if err := database.InitDatabase(); err != nil {
			logger.Errorf("初始化数据库失败: %v", err)
			log.Fatal(err)
		}
		defer func() {
			if err := database.CloseDatabase(); err != nil {
				logger.Errorf("关闭数据库连接失败: %v", err)
			}
		}()
	*/
	logger.Infof("当前使用模拟数据服务，无需数据库连接")

	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 设置路由
	router := routes.SetupRoutes()

	// 配置HTTP服务器
	server := &http.Server{
		Addr:    ":8000",
		Handler: router,
	}

	// 启动服务器的goroutine
	go func() {
		logger.Infof("服务器启动在端口 :8000")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Errorf("服务器启动失败: %v", err)
			log.Fatal(err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Infof("正在关闭服务器...")

	// 给服务器5秒时间来完成现有请求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("服务器强制关闭: %v", err)
		log.Fatal(err)
	}

	logger.Infof("服务器已关闭")
}
