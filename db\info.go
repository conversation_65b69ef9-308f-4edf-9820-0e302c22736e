package db

import (
	"fmt"
	"time"

	"train_expert_system/models"

	logger "github.com/sirupsen/logrus"

	"gorm.io/gorm"
)

// QueryInfo 查询info表数据，支持分页
func QueryInfo(limit, offset int, countTotal bool) ([]models.Info, int64, error) {
	startTime := time.Now()
	logger.Infof("开始查询info表，限制 %d 行，偏移量 %d", limit, offset)

	db, err := GetDB()
	if err != nil {
		return nil, 0, fmt.Errorf("无法获取数据库连接: %v", err)
	}

	var results []models.Info
	var total int64

	// 查询数据
	query := db.Model(&models.Info{}).Offset(offset).Limit(limit)
	if err := query.Find(&results).Error; err != nil {
		logger.Errorf("查询失败: %v", err)
		return nil, 0, fmt.Errorf("查询数据失败: %v", err)
	}

	// 如果需要，获取总记录数
	if countTotal {
		if err := db.Model(&models.Info{}).Count(&total).Error; err != nil {
			logger.Errorf("获取总记录数失败: %v", err)
			return nil, 0, fmt.Errorf("获取总记录数失败: %v", err)
		}
	}

	duration := time.Since(startTime)
	logger.Infof("查询成功，返回 %d 条记录，耗时 %.3f 秒", len(results), duration.Seconds())

	return results, total, nil
}

// InsertInfo 插入单条记录
func InsertInfo(info *models.Info) error {
	startTime := time.Now()
	logger.Infof("开始插入单条记录，name=%s", info.Name)

	db, err := GetDB()
	if err != nil {
		return fmt.Errorf("无法获取数据库连接: %v", err)
	}

	if err := db.Create(info).Error; err != nil {
		logger.Errorf("插入失败: %v", err)
		return fmt.Errorf("插入记录失败: %v", err)
	}

	duration := time.Since(startTime)
	logger.Infof("插入成功，ID=%d，耗时 %.3f 秒", info.ID, duration.Seconds())

	return nil
}

// BatchInsertInfo 批量插入记录
func BatchInsertInfo(items []models.Info) error {
	startTime := time.Now()
	count := len(items)
	logger.Infof("开始批量插入 %d 条记录", count)

	db, err := GetDB()
	if err != nil {
		return fmt.Errorf("无法获取数据库连接: %v", err)
	}

	// 使用事务进行批量插入
	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(items, 100).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		logger.Errorf("批量插入失败: %v", err)
		return fmt.Errorf("批量插入失败: %v", err)
	}

	duration := time.Since(startTime)
	perRecord := duration.Seconds() / float64(count)
	logger.Infof("批量插入成功，共 %d 条记录，总耗时 %.3f 秒，平均每条 %.5f 秒",
		count, duration.Seconds(), perRecord)

	return nil
}
