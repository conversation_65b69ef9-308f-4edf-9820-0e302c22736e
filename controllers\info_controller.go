package controllers

import (
	"net/http"
	"strconv"

	"train_expert_system/db"
	"train_expert_system/models"

	"github.com/gin-gonic/gin"
	logger "github.com/sirupsen/logrus"
)

var infoDB *db.InfoDB

func init() {
	var err error
	infoDB, err = db.NewInfoDB()
	if err != nil {
		logger.Fatalf("初始化InfoDB失败: %v", err)
	}
}

// GetInfo 分页查询info表数据
func GetInfo(ctx *gin.Context) {
	// 获取分页参数，设置默认值
	pageStr := ctx.DefaultQuery("page", "1")
	pageSizeStr := ctx.DefaultQuery("page_size", "10")

	// 转换参数
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		logger.Errorf("无效的页码参数: %s", pageStr)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的页码参数",
		})
		return
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 10000 {
		logger.Errorf("无效的页面大小参数: %s", pageSizeStr)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的页面大小参数，范围应在1-10000之间",
		})
		return
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	logger.Infof("收到查询请求，页码: %d, 页面大小: %d", page, pageSize)

	// 查询数据
	results, total, err := c.service.QueryInfoTable(pageSize, offset, true)
	if err != nil {
		c.logger.Errorf("查询数据失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "查询数据失败",
		})
		return
	}

	// 计算总页数
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	// 构建响应
	response := models.InfoResponse{
		Items:      results,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	c.logger.Infof("查询成功，返回 %d 条记录，总计 %d 条", len(results), total)

	ctx.JSON(http.StatusOK, response)
}

// CreateInfo 创建新的info记录
func (c *InfoController) CreateInfo(ctx *gin.Context) {
	var info models.Info

	// 绑定JSON数据到结构体
	if err := ctx.ShouldBindJSON(&info); err != nil {
		c.logger.Errorf("绑定JSON数据失败: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的JSON数据",
		})
		return
	}

	c.logger.Infof("收到创建请求，name: %s", info.Name)

	// 插入数据
	if err := c.service.InsertInfo(&info); err != nil {
		c.logger.Errorf("插入数据失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "插入数据失败",
		})
		return
	}

	c.logger.Infof("创建成功，ID: %d", info.ID)

	ctx.JSON(http.StatusCreated, gin.H{
		"message": "创建成功",
		"data":    info,
	})
}
